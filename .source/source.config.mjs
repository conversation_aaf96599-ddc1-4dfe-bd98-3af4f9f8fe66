// source.config.ts
import { defineDocs, defineCollections, frontmatterSchema } from "fumadocs-mdx/config";
import { z } from "zod";
var { docs, meta } = defineDocs({
  dir: "content/docs"
});
var blog = defineCollections({
  type: "doc",
  dir: "content/blog",
  schema: frontmatterSchema.extend({
    author: z.string().optional(),
    date: z.string().date().or(z.date()).optional(),
    tags: z.array(z.string()).optional()
  })
});
var changelog = defineCollections({
  type: "doc",
  dir: "content/changelog",
  schema: frontmatterSchema.extend({
    version: z.string(),
    date: z.string().date().or(z.date()),
    type: z.enum(["major", "minor", "patch"]).optional()
  })
});
export {
  blog,
  changelog,
  docs,
  meta
};
