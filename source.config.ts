import { defineDocs, defineCollections, frontmatterSchema } from 'fumadocs-mdx/config';
import { z } from 'zod';

// Define the main documentation collection
export const { docs, meta } = defineDocs({
  dir: 'content/docs',
});

// Define a blog collection for additional content (optional)
export const blog = defineCollections({
  type: 'doc',
  dir: 'content/blog',
  schema: frontmatterSchema.extend({
    author: z.string().optional(),
    date: z.string().date().or(z.date()).optional(),
    tags: z.array(z.string()).optional(),
  }),
});

// Define a changelog collection (optional)
export const changelog = defineCollections({
  type: 'doc',
  dir: 'content/changelog',
  schema: frontmatterSchema.extend({
    version: z.string(),
    date: z.string().date().or(z.date()),
    type: z.enum(['major', 'minor', 'patch']).optional(),
  }),
});
