import { docs, meta, blog, changelog } from '../../.source';
import { createMDXSource } from 'fumadocs-mdx';
import { loader } from 'fumadocs-core/source';
import type { InferMetaType, InferPageType } from 'fumadocs-core/source';

// Main documentation source
export const source = loader({
  baseUrl: '/docs',
  source: createMDXSource(docs, meta),
});

// Blog source (optional)
export const blogSource = loader({
  baseUrl: '/blog',
  source: createMDXSource(blog, []),
});

// Changelog source (optional)
export const changelogSource = loader({
  baseUrl: '/changelog',
  source: createMDXSource(changelog, []),
});

// Type exports for better TypeScript support
export type DocsPage = InferPageType<typeof source>;
export type DocsMeta = InferMetaType<typeof source>;
export type BlogPage = InferPageType<typeof blogSource>;
export type ChangelogPage = InferPageType<typeof changelogSource>;
