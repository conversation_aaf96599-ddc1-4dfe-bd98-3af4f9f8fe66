---
title: Authentication System
description: Complete user authentication with NextAuth.js, email verification, and OAuth providers
---

# Authentication System

ShipSaaS includes a comprehensive authentication system built with NextAuth.js, providing secure user management with multiple authentication methods.

## Features

### Core Authentication
- **Email/Password** - Traditional email and password authentication
- **OAuth Providers** - Google, GitHub, and other OAuth providers
- **Email Verification** - Secure email verification process
- **Password Reset** - Secure password reset with time-limited tokens
- **Session Management** - Secure session handling with JWT

### Security Features
- **CSRF Protection** - Built-in CSRF protection
- **Rate Limiting** - Prevent brute force attacks
- **Secure Cookies** - HTTP-only, secure cookies
- **Password Hashing** - bcrypt password hashing
- **Token Expiration** - Configurable token expiration

## Configuration

### NextAuth.js Setup

The authentication is configured in `src/auth.config.ts`:

```typescript
import { NextAuthConfig } from 'next-auth';
import Google from 'next-auth/providers/google';
import Credentials from 'next-auth/providers/credentials';

export const authConfig: NextAuthConfig = {
  providers: [
    Google({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    }),
    Credentials({
      async authorize(credentials) {
        // Custom credential validation
      },
    }),
  ],
  pages: {
    signIn: '/auth/signin',
    signUp: '/auth/signup',
    error: '/auth/error',
  },
  callbacks: {
    async session({ session, token }) {
      // Customize session data
    },
  },
};
```

### Environment Variables

Required environment variables:

```bash
# NextAuth.js
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"

# Google OAuth (optional)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# GitHub OAuth (optional)
GITHUB_ID="your-github-client-id"
GITHUB_SECRET="your-github-client-secret"
```

## Usage

### Sign Up Process

1. **User Registration**
   ```typescript
   const response = await fetch('/api/auth/signup', {
     method: 'POST',
     headers: { 'Content-Type': 'application/json' },
     body: JSON.stringify({
       email: '<EMAIL>',
       password: 'securepassword',
       name: 'John Doe'
     })
   });
   ```

2. **Email Verification**
   - User receives verification email
   - Clicks verification link
   - Account is activated

### Sign In Methods

#### Email/Password
```typescript
import { signIn } from 'next-auth/react';

await signIn('credentials', {
  email: '<EMAIL>',
  password: 'password',
  redirect: false
});
```

#### OAuth Providers
```typescript
import { signIn } from 'next-auth/react';

// Google OAuth
await signIn('google');

// GitHub OAuth
await signIn('github');
```

### Password Reset

1. **Request Reset**
   ```typescript
   const response = await fetch('/api/auth/reset-password', {
     method: 'POST',
     headers: { 'Content-Type': 'application/json' },
     body: JSON.stringify({ email: '<EMAIL>' })
   });
   ```

2. **Reset Password**
   ```typescript
   const response = await fetch('/api/auth/reset-password/confirm', {
     method: 'POST',
     headers: { 'Content-Type': 'application/json' },
     body: JSON.stringify({
       token: 'reset-token',
       password: 'newpassword'
     })
   });
   ```

### Session Management

#### Client-Side
```typescript
import { useSession } from 'next-auth/react';

function Profile() {
  const { data: session, status } = useSession();
  
  if (status === 'loading') return <p>Loading...</p>;
  if (status === 'unauthenticated') return <p>Not signed in</p>;
  
  return <p>Signed in as {session.user.email}</p>;
}
```

#### Server-Side
```typescript
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth.config';

export async function GET() {
  const session = await getServerSession(authConfig);
  
  if (!session) {
    return new Response('Unauthorized', { status: 401 });
  }
  
  return Response.json({ user: session.user });
}
```

## Customization

### Custom Sign-In Page

Create custom authentication pages in `src/app/[locale]/auth/`:

```typescript
// src/app/[locale]/auth/signin/page.tsx
import { SignInForm } from '@/components/auth/signin-form';

export default function SignInPage() {
  return (
    <div className="container mx-auto max-w-md">
      <h1>Sign In</h1>
      <SignInForm />
    </div>
  );
}
```

### Custom Callbacks

Customize authentication behavior:

```typescript
export const authConfig: NextAuthConfig = {
  callbacks: {
    async signIn({ user, account, profile }) {
      // Custom sign-in logic
      return true;
    },
    async session({ session, token }) {
      // Add custom data to session
      session.user.id = token.sub;
      return session;
    },
    async jwt({ token, user }) {
      // Customize JWT token
      if (user) {
        token.role = user.role;
      }
      return token;
    },
  },
};
```

### Role-Based Access

Implement role-based access control:

```typescript
// Middleware for protected routes
export function withAuth(handler: NextApiHandler, roles?: string[]) {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    const session = await getServerSession(req, res, authConfig);
    
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }
    
    if (roles && !roles.includes(session.user.role)) {
      return res.status(403).json({ error: 'Forbidden' });
    }
    
    return handler(req, res);
  };
}
```

## Security Best Practices

### Password Requirements
- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one number
- At least one special character

### Rate Limiting
```typescript
// Implement rate limiting for auth endpoints
import rateLimit from 'express-rate-limit';

const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window
  message: 'Too many authentication attempts'
});
```

### Session Security
- Secure cookie settings
- HTTPS in production
- Regular session rotation
- Proper logout handling

## Troubleshooting

### Common Issues

**OAuth Provider Setup**
- Verify client ID and secret
- Check redirect URLs
- Ensure proper scopes

**Email Verification**
- Check email service configuration
- Verify SMTP settings
- Test email delivery

**Session Issues**
- Clear browser cookies
- Check NEXTAUTH_SECRET
- Verify database connection

For more authentication details, see the [API Reference](/docs/api/authentication).
