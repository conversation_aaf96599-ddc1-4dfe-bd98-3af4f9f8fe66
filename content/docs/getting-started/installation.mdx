---
title: Installation
description: Step-by-step guide to install and set up ShipSaaS locally
---

# Installation Guide

This guide will walk you through setting up ShipSaaS on your local development environment.

## Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js 18+** - [Download from nodejs.org](https://nodejs.org/)
- **pnpm** - Package manager (recommended over npm/yarn)
- **Git** - Version control
- **PostgreSQL** - Database (or use a cloud provider)

### Installing pnpm

If you don't have pnpm installed:

```bash
npm install -g pnpm
```

## Step 1: Clone the Repository

```bash
git clone https://github.com/your-username/shipsaas-office.git
cd shipsaas-office
```

## Step 2: Install Dependencies

```bash
pnpm install
```

This will install all required dependencies including:
- Next.js 15
- React 18
- Tailwind CSS
- Prisma
- NextAuth.js
- Stripe
- And many more...

## Step 3: Environment Configuration

Copy the example environment file:

```bash
cp .env.example .env.local
```

Edit `.env.local` with your configuration:

```bash
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/shipsaas"

# NextAuth.js
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"

# Google OAuth (optional)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Stripe
STRIPE_SECRET_KEY="sk_test_..."
STRIPE_PUBLISHABLE_KEY="pk_test_..."
STRIPE_WEBHOOK_SECRET="whsec_..."

# GitHub Integration
GITHUB_TOKEN="ghp_..."

# Email (Resend)
RESEND_API_KEY="re_..."
RESEND_FROM_EMAIL="<EMAIL>"

# App Configuration
NEXT_PUBLIC_APP_URL="http://localhost:3000"
```

## Step 4: Database Setup

### Option A: Local PostgreSQL

1. Install PostgreSQL locally
2. Create a new database:
   ```sql
   CREATE DATABASE shipsaas;
   ```

### Option B: Cloud Database

Use a cloud provider like:
- [Supabase](https://supabase.com/) (recommended)
- [PlanetScale](https://planetscale.com/)
- [Railway](https://railway.app/)
- [Neon](https://neon.tech/)

### Initialize the Database

Run Prisma migrations:

```bash
pnpm db:push
```

Generate Prisma client:

```bash
pnpm db:generate
```

## Step 5: External Service Setup

### Stripe Configuration

1. Create a [Stripe account](https://stripe.com/)
2. Get your API keys from the Stripe dashboard
3. Set up webhooks pointing to `http://localhost:3000/api/stripe/webhook`
4. Configure your products and pricing in Stripe

### GitHub Integration (Optional)

1. Create a GitHub personal access token
2. Grant necessary permissions for repository management
3. Add the token to your environment variables

### Email Setup (Resend)

1. Sign up for [Resend](https://resend.com/)
2. Get your API key
3. Verify your sending domain

## Step 6: Start Development Server

```bash
pnpm dev
```

Your application will be available at `http://localhost:3000`.

## Verification

To verify everything is working correctly:

1. **Homepage** - Visit `http://localhost:3000`
2. **Authentication** - Try signing up/signing in
3. **Database** - Check if user data is saved
4. **Payments** - Test the payment flow (use Stripe test cards)

## Troubleshooting

### Common Issues

**Database Connection Error**
- Verify your `DATABASE_URL` is correct
- Ensure PostgreSQL is running
- Check firewall settings

**Stripe Webhook Issues**
- Use ngrok for local webhook testing
- Verify webhook endpoint URL
- Check webhook secret

**Build Errors**
- Clear `.next` folder: `rm -rf .next`
- Reinstall dependencies: `rm -rf node_modules && pnpm install`
- Check Node.js version compatibility

### Getting Help

If you encounter issues:

1. Check the [Troubleshooting Guide](/docs/advanced/troubleshooting)
2. Search existing GitHub issues
3. Create a new issue with detailed information

## Next Steps

Now that you have ShipSaaS running locally, check out the [Quick Start Guide](/docs/getting-started/quick-start) to learn how to customize it for your needs!
